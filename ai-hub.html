<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI中转站 - 智能分发平台</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            color: #ffd700;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 100px 5% 50px;
            text-align: center;
            color: white;
        }

        .hero {
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: none;
            cursor: pointer;
        }

        .cta-button:hover {
            background: #ff5252;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        /* 特性区域 */
        .features {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
            margin: 4rem 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            width: 250px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        /* 分发流程 */
        .distribution-process {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 3rem;
            margin: 4rem auto;
            max-width: 1000px;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            display: block;
            width: 50px;
            height: 3px;
            background: #ffd700;
            margin: 10px auto;
            border-radius: 3px;
        }

        .process-steps {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            position: relative;
        }

        .process-steps::before {
            content: '';
            position: absolute;
            top: 40px;
            left: 10%;
            width: 80%;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 1;
        }

        .step {
            text-align: center;
            width: 22%;
            position: relative;
            z-index: 2;
        }

        .step-number {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto 1rem;
            border: 5px solid white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* 统计数据 */
        .stats {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 3rem;
            margin: 4rem 0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 0.5rem;
        }

        /* 页脚 */
        .footer {
            background: rgba(0, 0, 0, 0.2);
            padding: 2rem 5%;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                padding: 1rem;
            }

            .nav-links {
                margin-top: 1rem;
            }

            .nav-links li {
                margin: 0 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .features {
                flex-direction: column;
                align-items: center;
            }

            .feature-card {
                width: 90%;
                max-width: 300px;
            }

            .distribution-process {
                padding: 2rem 1rem;
            }

            .process-steps::before {
                display: none;
            }

            .step {
                width: 100%;
                margin-bottom: 2rem;
            }

            .stats {
                gap: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-links li {
                margin: 0.5rem;
            }

            .hero h1 {
                font-size: 1.8rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .step-number {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <a href="#" class="logo">
            <span class="logo-icon">🤖</span>
            AI中转站
        </a>
        <ul class="nav-links">
            <li><a href="#features">功能特性</a></li>
            <li><a href="#process">分发流程</a></li>
            <li><a href="#stats">数据统计</a></li>
            <li><a href="#contact">联系我们</a></li>
        </ul>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
        <section class="hero">
            <h1>AI中转站 - 智能分发平台</h1>
            <p>高效、智能的AI模型分发解决方案，连接创新与应用</p>
            <a href="#process" class="cta-button">立即体验</a>
        </section>

        <!-- 特性区域 -->
        <section id="features" class="features">
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <h3>智能分发</h3>
                <p>基于AI算法智能匹配最适合的模型和资源</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>高速处理</h3>
                <p>优化的分发网络，确保快速响应和低延迟</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <h3>安全保障</h3>
                <p>多重安全机制，保护数据和模型安全</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <h3>实时监控</h3>
                <p>全方位监控分发状态和性能指标</p>
            </div>
        </section>

        <!-- 分发流程 -->
        <section id="process" class="distribution-process">
            <h2 class="section-title">分发流程</h2>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>需求分析</h3>
                    <p>分析用户需求和场景</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>模型匹配</h3>
                    <p>智能匹配最优AI模型</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>资源调度</h3>
                    <p>分配计算资源和网络</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h3>结果分发</h3>
                    <p>高效分发处理结果</p>
                </div>
            </div>
        </section>

        <!-- 统计数据 -->
        <section id="stats" class="stats">
            <div class="stat-item">
                <div class="stat-number">1000+</div>
                <div>AI模型</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">500+</div>
                <div>服务节点</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">99.9%</div>
                <div>可用性</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">10K+</div>
                <div>日活用户</div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <p>&copy; 2025 AI中转站. 保留所有权利.</p>
        <p>高效 · 智能 · 安全</p>
    </footer>
</body>
</html>